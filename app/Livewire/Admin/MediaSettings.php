<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\Property;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Livewire\WithPagination;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[Layout('components.layouts.admin', ['title' => 'Media Management'])]
final class MediaSettings extends Component
{
    use WithPagination;
    public $activeTab = 'conversions';

    public $isRegenerating = false;

    public $regenerationProgress = 0;

    public $regenerationTotal = 0;

    public $regenerationCurrent = 0;

    public $regenerationStartTime = null;

    public $regenerationEstimatedCompletion = null;

    public $regenerationSessionId = null;

    public $isProcessingQueue = false;

    public $selectedProperties = [];

    public $selectAll = false;

    public $showBulkActions = false;

    public $operationMessage = '';

    public $operationType = 'info'; // info, success, error, warning

    protected $listeners = [
        'refreshMediaStats' => '$refresh',
        'regenerationComplete' => 'handleRegenerationComplete',
        'updateProgress' => 'updateRegenerationProgress',
    ];

    public function mount(): void
    {
        // Authorization is handled by the route middleware (role:admin)
        // No additional gate check needed
    }

    public function render()
    {
        $mediaStats = $this->getMediaStatistics();
        $queueStats = $this->getQueueStatistics();
        $properties = $this->getPropertiesWithMedia();

        return view('livewire.admin.media-settings', ['mediaStats' => $mediaStats, 'queueStats' => $queueStats, 'properties' => $properties]);
    }

    public function setActiveTab($tab): void
    {
        $this->activeTab = $tab;
        $this->resetOperationMessage();

        // Reset pagination when switching tabs
        $this->resetPage();

        // Reset selection state
        $this->selectedProperties = [];
        $this->selectAll = false;
        $this->showBulkActions = false;
    }

    public function regenerateAllConversions(): void
    {
        if ($this->isRegenerating) {
            return;
        }

        $this->startRegeneration();
        $this->regenerationTotal = Media::count();

        Log::info('Admin initiated media conversion regeneration', [
            'user_id' => Auth::id(),
            'total_media' => $this->regenerationTotal,
            'session_id' => $this->regenerationSessionId,
        ]);

        try {
            // Use Artisan command to regenerate conversions
            Artisan::call('media-library:regenerate', [
                '--force' => true,
            ]);

            $this->setOperationMessage('All media conversions have been queued for regeneration. Starting batch processing...', 'success');

            // Start processing the queued jobs immediately
            $this->startBatchProcessing();

        } catch (Exception $e) {
            Log::error('Failed to regenerate media conversions', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'session_id' => $this->regenerationSessionId,
            ]);

            $this->setOperationMessage('Failed to regenerate conversions: '.$e->getMessage(), 'error');
            $this->stopRegeneration();
        }
    }

    public function regenerateSelectedConversions(): void
    {
        if (empty($this->selectedProperties)) {
            $this->setOperationMessage('Please select properties to regenerate conversions for.', 'warning');
            return;
        }

        if ($this->isRegenerating) {
            $this->setOperationMessage('A regeneration process is already running. Please wait for it to complete.', 'warning');
            return;
        }

        $properties = Property::whereIn('id', $this->selectedProperties)->with('media')->get();
        $mediaIds = [];
        $mediaCount = 0;

        foreach ($properties as $property) {
            foreach ($property->media as $media) {
                $mediaIds[] = $media->id;
                $mediaCount++;
            }
        }

        if ($mediaIds !== []) {
            $this->startRegeneration();
            $this->regenerationTotal = $mediaCount;

            try {
                // Use Artisan command to regenerate conversions for specific media IDs
                Artisan::call('media-library:regenerate', [
                    '--ids' => implode(',', $mediaIds),
                    '--force' => true,
                ]);

                Log::info('Admin initiated selective media conversion regeneration', [
                    'user_id' => Auth::id(),
                    'property_ids' => $this->selectedProperties,
                    'media_count' => $mediaCount,
                    'media_ids' => $mediaIds,
                    'session_id' => $this->regenerationSessionId,
                ]);

                $this->setOperationMessage("Queued regeneration for {$mediaCount} media files from ".count($this->selectedProperties).' properties. Starting batch processing...', 'success');

                // Start processing the queued jobs immediately
                $this->startBatchProcessing();
            } catch (Exception $e) {
                Log::error('Failed to regenerate selected media conversions', [
                    'error' => $e->getMessage(),
                    'user_id' => Auth::id(),
                    'property_ids' => $this->selectedProperties,
                    'session_id' => $this->regenerationSessionId,
                ]);

                $this->setOperationMessage('Failed to regenerate conversions: '.$e->getMessage(), 'error');
                $this->stopRegeneration();
            }
        } else {
            $this->setOperationMessage('No media found for selected properties.', 'warning');
        }

        $this->selectedProperties = [];
        $this->selectAll = false;
        $this->showBulkActions = false;
    }

    public function clearConversionCache(): void
    {
        try {
            // Clear storage size cache specifically
            Cache::forget('media_storage_size');

            // Clear Laravel cache
            Artisan::call('cache:clear');

            // Clear media conversion cache if exists
            if (Storage::disk('public')->exists('conversions')) {
                Storage::disk('public')->deleteDirectory('conversions');
            }

            Log::info('Admin cleared media conversion cache', [
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Media conversion cache cleared successfully. Storage size will be recalculated.', 'success');

        } catch (Exception $e) {
            Log::error('Failed to clear media conversion cache', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Failed to clear cache: '.$e->getMessage(), 'error');
        }
    }

    public function refreshStorageSize(): void
    {
        try {
            // Force recalculation of storage size
            Cache::forget('media_storage_size');
            $newSize = $this->calculateStorageSize();

            $this->setOperationMessage("Storage size refreshed: {$newSize}", 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to refresh storage size: '.$e->getMessage(), 'error');
        }
    }

    public function processQueueJobs(): void
    {
        try {
            if ($this->isRegenerating) {
                $this->setOperationMessage('Cannot manually process jobs while regeneration is in progress.', 'warning');
                return;
            }

            // Process a batch of queue jobs
            Artisan::call('queue:work', [
                '--once' => true,
                '--timeout' => 60,
            ]);

            $this->setOperationMessage('Queue jobs processed successfully.', 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to process queue jobs: '.$e->getMessage(), 'error');
        }
    }

    public function startBatchProcessing(): void
    {
        if ($this->isProcessingQueue) {
            return;
        }

        $this->isProcessingQueue = true;

        try {
            // Process multiple jobs in batches to handle the regeneration efficiently
            $this->processBatchJobs();
        } catch (Exception $e) {
            Log::error('Failed to start batch processing', [
                'error' => $e->getMessage(),
                'session_id' => $this->regenerationSessionId,
            ]);
            $this->setOperationMessage('Failed to start batch processing: '.$e->getMessage(), 'error');
        }

        $this->isProcessingQueue = false;
    }

    private function processBatchJobs(): void
    {
        $maxBatchSize = 10; // Process up to 10 jobs per batch
        $maxBatches = 20;   // Maximum number of batches to prevent infinite loops
        $batchCount = 0;

        while ($batchCount < $maxBatches) {
            // Check if there are any media conversion jobs pending
            $mediaJobsCount = DB::table('jobs')
                ->where('payload', 'like', '%PerformConversionsJob%')
                ->count();

            if ($mediaJobsCount === 0) {
                // No more media jobs to process
                break;
            }

            try {
                // Process a batch of jobs
                Artisan::call('queue:work', [
                    '--max-jobs' => $maxBatchSize,
                    '--timeout' => 300, // 5 minutes timeout for batch
                    '--stop-when-empty' => true,
                ]);

                $batchCount++;

                // Small delay to prevent overwhelming the system
                usleep(100000); // 0.1 second delay

            } catch (Exception $e) {
                Log::error('Batch processing failed', [
                    'batch' => $batchCount,
                    'error' => $e->getMessage(),
                    'session_id' => $this->regenerationSessionId,
                ]);
                break;
            }
        }

        Log::info('Batch processing completed', [
            'batches_processed' => $batchCount,
            'session_id' => $this->regenerationSessionId,
        ]);
    }

    public function retryFailedJobs(): void
    {
        try {
            Artisan::call('queue:retry', ['--all' => true]);

            Log::info('Admin retried all failed queue jobs', [
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('All failed jobs have been retried.', 'success');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to retry jobs: '.$e->getMessage(), 'error');
        }
    }

    public function clearFailedJobs(): void
    {
        try {
            // Get count of failed jobs before clearing
            $totalFailedJobs = DB::table('failed_jobs')->count();
            $failedMediaJobs = DB::table('failed_jobs')->where('payload', 'like', '%PerformConversionsJob%')->count();

            if ($totalFailedJobs === 0) {
                $this->setOperationMessage('No failed jobs to clear.', 'info');
                return;
            }

            // Use Artisan command to clear failed jobs
            Artisan::call('queue:flush');

            Log::info('Admin cleared all failed queue jobs', [
                'user_id' => Auth::id(),
                'total_failed_jobs_cleared' => $totalFailedJobs,
                'failed_media_jobs_cleared' => $failedMediaJobs,
            ]);

            $this->setOperationMessage("Cleared {$totalFailedJobs} failed jobs ({$failedMediaJobs} media conversion jobs).", 'success');

        } catch (Exception $e) {
            Log::error('Failed to clear failed jobs', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Failed to clear failed jobs: '.$e->getMessage(), 'error');
        }
    }

    public function toggleSelectAll(): void
    {
        if ($this->selectAll) {
            // Get current page properties only
            $currentPageProperties = $this->getPropertiesWithMedia();
            $this->selectedProperties = $currentPageProperties->pluck('id')->toArray();
        } else {
            $this->selectedProperties = [];
        }

        $this->showBulkActions = ! empty($this->selectedProperties);
    }

    public function updatedSelectedProperties(): void
    {
        $this->showBulkActions = ! empty($this->selectedProperties);

        // Get current page properties for comparison
        $currentPageProperties = $this->getPropertiesWithMedia();
        $currentPageIds = $currentPageProperties->pluck('id')->toArray();

        // Check if all current page items are selected
        $this->selectAll = count($currentPageIds) > 0 &&
                          count(array_intersect($this->selectedProperties, $currentPageIds)) === count($currentPageIds);
    }

    public function handleRegenerationComplete(): void
    {
        $this->stopRegeneration();
        $this->regenerationProgress = 100;
        $this->setOperationMessage('Media conversion regeneration completed successfully.', 'success');
    }

    public function updateRegenerationProgress(): void
    {
        if (!$this->isRegenerating || !$this->regenerationSessionId) {
            return;
        }

        $this->updateProgressFromQueue();
    }

    private function startRegeneration(): void
    {
        $this->isRegenerating = true;
        $this->regenerationProgress = 0;
        $this->regenerationCurrent = 0;
        $this->regenerationStartTime = now();
        $this->regenerationSessionId = uniqid('regen_', true);
        $this->regenerationEstimatedCompletion = null;

        // Store regeneration session in cache for tracking
        Cache::put("media_regeneration_{$this->regenerationSessionId}", [
            'total' => $this->regenerationTotal,
            'current' => 0,
            'started_at' => $this->regenerationStartTime,
            'user_id' => Auth::id(),
        ], now()->addHours(2));
    }

    private function stopRegeneration(): void
    {
        $this->isRegenerating = false;
        $this->isProcessingQueue = false;

        if ($this->regenerationSessionId) {
            Cache::forget("media_regeneration_{$this->regenerationSessionId}");
            $this->regenerationSessionId = null;
        }

        $this->regenerationStartTime = null;
        $this->regenerationEstimatedCompletion = null;
    }

    public function stopBatchProcessing(): void
    {
        try {
            $this->isProcessingQueue = false;

            Log::info('Admin stopped batch processing', [
                'user_id' => Auth::id(),
                'session_id' => $this->regenerationSessionId,
            ]);

            $this->setOperationMessage('Batch processing stopped. Remaining jobs will stay in queue.', 'warning');

        } catch (Exception $e) {
            $this->setOperationMessage('Failed to stop batch processing: '.$e->getMessage(), 'error');
        }
    }

    public function clearPendingJobs(): void
    {
        try {
            // Get count of jobs before clearing
            $totalJobs = DB::table('jobs')->count();
            $mediaJobs = DB::table('jobs')->where('payload', 'like', '%PerformConversionsJob%')->count();

            if ($totalJobs === 0) {
                $this->setOperationMessage('No pending jobs to clear.', 'info');
                return;
            }

            // Clear all pending jobs
            DB::table('jobs')->delete();

            // Stop any ongoing regeneration process
            $this->stopRegeneration();

            Log::info('Admin cleared pending queue jobs', [
                'user_id' => Auth::id(),
                'total_jobs_cleared' => $totalJobs,
                'media_jobs_cleared' => $mediaJobs,
            ]);

            $this->setOperationMessage("Cleared {$totalJobs} pending jobs ({$mediaJobs} media conversion jobs).", 'success');

        } catch (Exception $e) {
            Log::error('Failed to clear pending jobs', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            $this->setOperationMessage('Failed to clear pending jobs: '.$e->getMessage(), 'error');
        }
    }



    private function updateProgressFromQueue(): void
    {
        if (!$this->regenerationSessionId || !$this->regenerationTotal) {
            return;
        }

        // Get current queue statistics
        $queueStats = $this->getQueueStatistics();
        $pendingMediaJobs = $queueStats['media_jobs'];

        // Calculate progress based on remaining jobs
        if ($this->regenerationTotal > 0) {
            $completed = max(0, $this->regenerationTotal - $pendingMediaJobs);
            $this->regenerationCurrent = $completed;
            $this->regenerationProgress = min(100, round(($completed / $this->regenerationTotal) * 100, 1));

            // Calculate estimated completion time with better accuracy for batch processing
            if ($completed > 0 && $this->regenerationStartTime) {
                $elapsed = now()->diffInSeconds($this->regenerationStartTime);

                // Adjust time calculation for batch processing (faster than single job processing)
                $avgTimePerJob = $completed > 10 ? $elapsed / $completed : 2; // Assume 2 seconds per job initially
                $remainingJobs = $this->regenerationTotal - $completed;

                // Account for batch processing efficiency
                $batchEfficiencyFactor = 0.7; // Batch processing is typically 30% faster
                $estimatedSecondsRemaining = ($remainingJobs * $avgTimePerJob) * $batchEfficiencyFactor;

                $this->regenerationEstimatedCompletion = now()->addSeconds($estimatedSecondsRemaining);
            }
        }

        // Auto-complete when no more media jobs are pending and we've processed enough
        if ($pendingMediaJobs === 0 && $this->regenerationCurrent > 0) {
            // Give a small buffer to ensure all jobs are truly complete
            if ($this->regenerationCurrent >= ($this->regenerationTotal * 0.95)) {
                $this->handleRegenerationComplete();
            }
        }
    }

    private function getMediaStatistics(): array
    {
        try {
            $totalMedia = Media::count();

            // Get conversion statistics using proper JSON queries
            $conversionStats = Media::selectRaw('
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.thumb") = true THEN 1 ELSE 0 END) as thumb_count,
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.preview") = true THEN 1 ELSE 0 END) as preview_count,
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.thumb") = true AND JSON_EXTRACT(generated_conversions, "$.preview") = true THEN 1 ELSE 0 END) as complete_conversions
            ')->first();

            $thumbCount = (int) ($conversionStats->thumb_count ?? 0);
            $previewCount = (int) ($conversionStats->preview_count ?? 0);
            $completeConversions = (int) ($conversionStats->complete_conversions ?? 0);

            // Calculate media with at least one conversion
            $mediaWithConversions = Media::whereRaw('
                JSON_EXTRACT(generated_conversions, "$.thumb") = true OR
                JSON_EXTRACT(generated_conversions, "$.preview") = true
            ')->count();

            $mediaWithoutConversions = $totalMedia - $mediaWithConversions;

            // Calculate success rate based on complete conversions (both thumb and preview)
            $conversionSuccessRate = $totalMedia > 0 ? round(($completeConversions / $totalMedia) * 100, 1) : 0;

            $storageSize = $this->calculateStorageSize();
        } catch (Exception $e) {
            Log::error('Failed to get media statistics', ['error' => $e->getMessage()]);

            // Return safe defaults
            return [
                'total_media' => 0,
                'with_conversions' => 0,
                'without_conversions' => 0,
                'complete_conversions' => 0,
                'thumb_conversions' => 0,
                'preview_conversions' => 0,
                'storage_size' => 'Unable to calculate',
                'conversion_success_rate' => 0,
                'thumb_success_rate' => 0,
                'preview_success_rate' => 0,
            ];
        }

        return [
            'total_media' => $totalMedia,
            'with_conversions' => $mediaWithConversions,
            'without_conversions' => $mediaWithoutConversions,
            'complete_conversions' => $completeConversions,
            'thumb_conversions' => $thumbCount,
            'preview_conversions' => $previewCount,
            'storage_size' => $storageSize,
            'conversion_success_rate' => $conversionSuccessRate,
            'thumb_success_rate' => $totalMedia > 0 ? round(($thumbCount / $totalMedia) * 100, 1) : 0,
            'preview_success_rate' => $totalMedia > 0 ? round(($previewCount / $totalMedia) * 100, 1) : 0,
        ];
    }

    public function getQueueStatistics(): array
    {
        try {
            $pendingJobs = DB::table('jobs')->count();
            $failedJobs = DB::table('failed_jobs')->count();

            // Get media conversion specific jobs with more detail
            $mediaJobs = DB::table('jobs')
                ->where('payload', 'like', '%PerformConversionsJob%')
                ->count();

            // Get failed media conversion jobs
            $failedMediaJobs = DB::table('failed_jobs')
                ->where('payload', 'like', '%PerformConversionsJob%')
                ->count();

            // Calculate processing statistics
            $totalProcessedJobs = $mediaJobs + $failedMediaJobs;
            $mediaJobSuccessRate = $totalProcessedJobs > 0 ?
                round((($totalProcessedJobs - $failedMediaJobs) / $totalProcessedJobs) * 100, 1) : 100;

            return [
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs,
                'media_jobs' => $mediaJobs,
                'failed_media_jobs' => $failedMediaJobs,
                'media_job_success_rate' => $mediaJobSuccessRate,
                'queue_status' => $this->getQueueWorkerStatus(),
                'last_failed_job' => $this->getLastFailedJob(),
            ];
        } catch (Exception $e) {
            Log::error('Failed to get queue statistics', ['error' => $e->getMessage()]);

            // Return safe defaults
            return [
                'pending_jobs' => 0,
                'failed_jobs' => 0,
                'media_jobs' => 0,
                'failed_media_jobs' => 0,
                'media_job_success_rate' => 100,
                'queue_status' => 'unknown',
                'last_failed_job' => null,
            ];
        }
    }

    private function getQueueWorkerStatus(): string
    {
        // Simple check - in production you might want to use Horizon or Supervisor status
        $processes = shell_exec('ps aux | grep "queue:work" | grep -v grep | wc -l');

        return (int) $processes > 0 ? 'running' : 'stopped';
    }

    private function getLastFailedJob(): ?array
    {
        $lastFailedJob = DB::table('failed_jobs')
            ->where('payload', 'like', '%PerformConversionsJob%')
            ->orderBy('failed_at', 'desc')
            ->first();

        if (!$lastFailedJob) {
            return null;
        }

        return [
            'failed_at' => $lastFailedJob->failed_at,
            'exception' => substr($lastFailedJob->exception, 0, 200) . '...', // Truncate for display
        ];
    }

    private function calculateStorageSize(): string
    {
        try {
            // Use caching to avoid expensive file system operations on every request
            return Cache::remember('media_storage_size', now()->addMinutes(5), function () {
                return $this->performStorageSizeCalculation();
            });
        } catch (Exception $e) {
            Log::error('Failed to calculate storage size', ['error' => $e->getMessage()]);
            return 'Unable to calculate';
        }
    }

    private function performStorageSizeCalculation(): string
    {
        $totalSize = 0;
        $originalFilesSize = 0;
        $conversionsSize = 0;
        $scannedFiles = 0;
        $errors = 0;

        try {
            // Get all media records with their file paths
            $mediaFiles = Media::select('id', 'file_name', 'size', 'disk', 'generated_conversions')->get();

            foreach ($mediaFiles as $media) {
                try {
                    // Add original file size from database (more reliable than file system)
                    $originalFilesSize += $media->size;
                    $scannedFiles++;

                    // Calculate actual conversion file sizes
                    $conversionsSize += $this->calculateMediaConversionsSize($media);

                } catch (Exception $e) {
                    $errors++;
                    Log::warning('Failed to calculate size for media ID: ' . $media->id, [
                        'error' => $e->getMessage(),
                        'file_name' => $media->file_name,
                    ]);
                }
            }

            $totalSize = $originalFilesSize + $conversionsSize;

            // Log calculation summary for monitoring
            Log::info('Storage size calculation completed', [
                'total_size' => $totalSize,
                'original_files_size' => $originalFilesSize,
                'conversions_size' => $conversionsSize,
                'files_scanned' => $scannedFiles,
                'errors' => $errors,
                'formatted_size' => $this->formatBytes($totalSize),
            ]);

        } catch (Exception $e) {
            Log::error('Failed to perform storage size calculation', ['error' => $e->getMessage()]);
            return 'Unable to calculate';
        }

        return $this->formatBytes($totalSize);
    }

    private function calculateMediaConversionsSize(Media $media): int
    {
        $conversionsSize = 0;
        $generatedConversions = $media->generated_conversions;

        if (!is_array($generatedConversions)) {
            return 0;
        }

        // Get the media disk
        $disk = Storage::disk($media->disk);

        foreach ($generatedConversions as $conversionName => $isGenerated) {
            if (!$isGenerated) {
                continue;
            }

            try {
                // Build the conversion file path
                $conversionPath = $media->id . '/conversions/' . pathinfo($media->file_name, PATHINFO_FILENAME) . '-' . $conversionName . '.' . pathinfo($media->file_name, PATHINFO_EXTENSION);

                if ($disk->exists($conversionPath)) {
                    $conversionsSize += $disk->size($conversionPath);
                }
            } catch (Exception $e) {
                // Log but don't fail the entire calculation for one conversion
                Log::debug('Failed to get conversion size', [
                    'media_id' => $media->id,
                    'conversion' => $conversionName,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $conversionsSize;
    }

    private function formatBytes(int $bytes, $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision).' '.$units[$i];
    }

    private function getPropertiesWithMedia()
    {
        return Property::with(['media', 'user'])
            ->whereHas('media')
            ->orderBy('updated_at', 'desc')
            ->paginate(20);
    }

    private function setOperationMessage(string $message, string $type = 'info'): void
    {
        $this->operationMessage = $message;
        $this->operationType = $type;
    }

    private function resetOperationMessage(): void
    {
        $this->operationMessage = '';
        $this->operationType = 'info';
    }
}
