<?php

declare(strict_types=1);

namespace App\Livewire\Admin;

use App\Models\Property;
use App\Models\PropertyType;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Layout;
use Livewire\Component;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

#[Layout('components.layouts.admin', ['title' => 'Admin Dashboard'])]
final class Dashboard extends Component
{
    public function render()
    {
        // Basic counts
        $totalUsers = User::count();
        $totalProperties = Property::count();
        $publishedProperties = Property::where('status', 'published')->count();
        $pendingProperties = Property::where('status', 'draft')->count();

        // User statistics by role
        $adminUsers = User::role('admin')->count();
        $agentUsers = User::role('agent')->count();
        $seekerUsers = User::role('seeker')->count();
        $activeUsers = User::where('is_active', true)->count();
        $inactiveUsers = User::where('is_active', false)->count();

        // Property statistics by status
        $soldProperties = Property::where('status', 'sold')->count();
        $rentedProperties = Property::where('status', 'rented')->count();
        $underOfferProperties = Property::where('status', 'under_offer')->count();

        // Property types breakdown
        $propertyTypes = PropertyType::withCount('properties')
            ->get()
            ->pluck('properties_count', 'name')
            ->toArray();
        $propertyTypePercentages = [];
        foreach ($propertyTypes as $type => $count) {
            $propertyTypePercentages[$type] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        // Listing types breakdown
        $listingTypes = Property::selectRaw('listing_type, COUNT(*) as count')
            ->groupBy('listing_type')
            ->pluck('count', 'listing_type')
            ->toArray();
        $listingTypePercentages = [];
        foreach ($listingTypes as $type => $count) {
            $listingTypePercentages[$type] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        // Recent activity - last 5 properties and users
        $recentProperties = Property::with('user')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        $recentUsers = User::orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Average property price
        $averagePrice = Property::where('status', 'published')->avg('price');

        // Properties by city (top 5)
        $topCities = Property::selectRaw('city, COUNT(*) as count')
            ->groupBy('city')
            ->orderBy('count', 'desc')
            ->limit(5)
            ->pluck('count', 'city')
            ->toArray();
        $topCityPercentages = [];
        foreach ($topCities as $city => $count) {
            $topCityPercentages[$city] = number_format(($count / max($totalProperties, 1)) * 100, 2);
        }

        // Media health statistics (improved)
        $mediaStats = $this->getMediaHealthStatistics();

        // Queue health statistics
        $pendingJobs = DB::table('jobs')->count();
        $failedJobs = DB::table('failed_jobs')->count();
        $mediaJobs = DB::table('jobs')->where('payload', 'like', '%PerformConversionsJob%')->count();

        // Determine media health status based on improved metrics
        $mediaHealthStatus = $this->determineMediaHealthStatus($mediaStats, $failedJobs);

        return view('livewire.admin.dashboard', [
            'totalUsers' => $totalUsers,
            'totalProperties' => $totalProperties,
            'publishedProperties' => $publishedProperties,
            'pendingProperties' => $pendingProperties,
            'adminUsers' => $adminUsers,
            'agentUsers' => $agentUsers,
            'seekerUsers' => $seekerUsers,
            'activeUsers' => $activeUsers,
            'inactiveUsers' => $inactiveUsers,
            'soldProperties' => $soldProperties,
            'rentedProperties' => $rentedProperties,
            'underOfferProperties' => $underOfferProperties,
            'propertyTypes' => $propertyTypes,
            'propertyTypePercentages' => $propertyTypePercentages,
            'listingTypes' => $listingTypes,
            'listingTypePercentages' => $listingTypePercentages,
            'topCities' => $topCities,
            'topCityPercentages' => $topCityPercentages,
            'averagePrice' => $averagePrice,
            'recentProperties' => $recentProperties,
            'recentUsers' => $recentUsers,
            'mediaStats' => $mediaStats,
            'pendingJobs' => $pendingJobs,
            'failedJobs' => $failedJobs,
            'mediaJobs' => $mediaJobs,
            'mediaHealthStatus' => $mediaHealthStatus,
        ]);
    }

    private function getMediaHealthStatistics(): array
    {
        try {
            $totalMedia = Media::count();

            // Get conversion statistics using proper JSON queries
            $conversionStats = Media::selectRaw('
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.thumb") = true THEN 1 ELSE 0 END) as thumb_count,
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.preview") = true THEN 1 ELSE 0 END) as preview_count,
                SUM(CASE WHEN JSON_EXTRACT(generated_conversions, "$.thumb") = true AND JSON_EXTRACT(generated_conversions, "$.preview") = true THEN 1 ELSE 0 END) as complete_conversions
            ')->first();

            $thumbCount = (int) ($conversionStats->thumb_count ?? 0);
            $previewCount = (int) ($conversionStats->preview_count ?? 0);
            $completeConversions = (int) ($conversionStats->complete_conversions ?? 0);

            // Calculate media with at least one conversion
            $mediaWithConversions = Media::whereRaw('
                JSON_EXTRACT(generated_conversions, "$.thumb") = true OR
                JSON_EXTRACT(generated_conversions, "$.preview") = true
            ')->count();

            $mediaWithoutConversions = $totalMedia - $mediaWithConversions;

            // Calculate success rates
            $conversionSuccessRate = $totalMedia > 0 ? round(($completeConversions / $totalMedia) * 100, 1) : 0;
            $thumbSuccessRate = $totalMedia > 0 ? round(($thumbCount / $totalMedia) * 100, 1) : 0;
            $previewSuccessRate = $totalMedia > 0 ? round(($previewCount / $totalMedia) * 100, 1) : 0;

            return [
                'total_media' => $totalMedia,
                'with_conversions' => $mediaWithConversions,
                'without_conversions' => $mediaWithoutConversions,
                'complete_conversions' => $completeConversions,
                'thumb_conversions' => $thumbCount,
                'preview_conversions' => $previewCount,
                'conversion_success_rate' => $conversionSuccessRate,
                'thumb_success_rate' => $thumbSuccessRate,
                'preview_success_rate' => $previewSuccessRate,
            ];
        } catch (Exception $e) {
            Log::error('Failed to get media health statistics for dashboard', ['error' => $e->getMessage()]);

            // Return safe defaults
            return [
                'total_media' => 0,
                'with_conversions' => 0,
                'without_conversions' => 0,
                'complete_conversions' => 0,
                'thumb_conversions' => 0,
                'preview_conversions' => 0,
                'conversion_success_rate' => 0,
                'thumb_success_rate' => 0,
                'preview_success_rate' => 0,
            ];
        }
    }

    private function determineMediaHealthStatus(array $mediaStats, int $failedJobs): string
    {
        $conversionSuccessRate = $mediaStats['conversion_success_rate'];
        $totalMedia = $mediaStats['total_media'];

        // Critical conditions
        if ($conversionSuccessRate < 50 || $failedJobs > 10 || ($totalMedia > 0 && $mediaStats['complete_conversions'] === 0)) {
            return 'critical';
        }

        // Warning conditions
        if ($conversionSuccessRate < 80 || $failedJobs > 5 || $mediaStats['without_conversions'] > ($totalMedia * 0.2)) {
            return 'warning';
        }

        // Healthy
        return 'healthy';
    }
}
