<div wire:poll.2s="updateRegenerationProgress">
    <!-- Header -->
    <div class="mb-8">
        <flux:heading size="xl">Media Management</flux:heading>
        <flux:subheading>Manage image conversions, queue processing, and media storage</flux:subheading>
    </div>

    <!-- Operation Messages -->
    @if($operationMessage)
        <div class="mb-6">
            <div class="p-4 rounded-lg border {{
                $operationType === 'error' ? 'bg-red-50 border-red-200 text-red-800' : (
                $operationType === 'warning' ? 'bg-yellow-50 border-yellow-200 text-yellow-800' : (
                $operationType === 'success' ? 'bg-green-50 border-green-200 text-green-800' :
                'bg-blue-50 border-blue-200 text-blue-800'
                ))
            }}">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($operationType === 'error')
                            <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                        @elseif($operationType === 'warning')
                            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                            </svg>
                        @elseif($operationType === 'success')
                            <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        @else
                            <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                            </svg>
                        @endif
                    </div>
                    <div class="ml-3">
                        <p class="text-sm font-medium">{{ $operationMessage }}</p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Tab Navigation -->
    <div class="mb-8">
        <div class="border-b border-gray-200">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <button wire:click="$set('activeTab', 'conversions')"
                        class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'conversions' ? 'border-indigo-500 text-indigo-600' : '' }}">
                    Media Conversions
                </button>
                <button wire:click="$set('activeTab', 'queue')"
                        class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'queue' ? 'border-indigo-500 text-indigo-600' : '' }}">
                    Queue Management
                </button>
                <button wire:click="$set('activeTab', 'bulk')"
                        class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'bulk' ? 'border-indigo-500 text-indigo-600' : '' }}">
                    Bulk Operations
                </button>
                <button wire:click="$set('activeTab', 'statistics')"
                        class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm {{ $activeTab === 'statistics' ? 'border-indigo-500 text-indigo-600' : '' }}">
                    Statistics
                </button>
            </nav>
        </div>
    </div>

    <!-- Media Conversions Tab -->
    @if($activeTab === 'conversions')
        <div class="space-y-6">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="xl" class="text-blue-600">{{ $mediaStats['total_media'] }}</flux:heading>
                            <flux:subheading>Total Media Files</flux:subheading>
                            <div class="text-xs text-gray-500 mt-1">
                                Storage: {{ $mediaStats['storage_size'] }}
                            </div>
                        </div>
                        <flux:icon.photo class="size-8 text-blue-500" />
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="xl" class="text-green-600">{{ $mediaStats['complete_conversions'] }}</flux:heading>
                            <flux:subheading>Complete Conversions</flux:subheading>
                            <div class="text-xs text-gray-500 mt-1">
                                {{ $mediaStats['conversion_success_rate'] }}% success rate (both thumb & preview)
                            </div>
                        </div>
                        <flux:icon.check-circle class="size-8 text-green-500" />
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="xl" class="text-orange-600">{{ $mediaStats['without_conversions'] }}</flux:heading>
                            <flux:subheading>Missing Conversions</flux:subheading>
                            <div class="text-xs text-gray-500 mt-1">
                                Need regeneration
                            </div>
                        </div>
                        <flux:icon.exclamation-triangle class="size-8 text-orange-500" />
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="xl" class="text-purple-600">{{ $mediaStats['thumb_conversions'] }}</flux:heading>
                            <flux:subheading>Thumbnail Conversions</flux:subheading>
                            <div class="text-xs text-gray-500 mt-1">
                                {{ $mediaStats['thumb_success_rate'] }}% thumb success rate
                            </div>
                        </div>
                        <flux:icon.squares-2x2 class="size-8 text-purple-500" />
                    </div>
                </div>
            </div>

            <!-- Conversion Management Actions -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="mb-6">
                    <flux:heading size="lg">Conversion Management</flux:heading>
                    <flux:subheading>Regenerate image conversions and manage conversion cache</flux:subheading>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <flux:button
                        wire:click="regenerateAllConversions"
                        variant="primary"
                        class="justify-center py-3"
                        :disabled="$isRegenerating"
                    >
                        <flux:icon.arrow-path class="size-5 mr-2" />
                        @if($isRegenerating)
                            Regenerating...
                        @else
                            Regenerate All Conversions
                        @endif
                    </flux:button>

                    <flux:button
                        wire:click="clearConversionCache"
                        variant="outline"
                        class="justify-center py-3"
                    >
                        <flux:icon.trash class="size-5 mr-2" />
                        Clear Conversion Cache
                    </flux:button>

                    <flux:button
                        wire:click="refreshStorageSize"
                        variant="outline"
                        class="justify-center py-3"
                    >
                        <flux:icon.arrow-path class="size-5 mr-2" />
                        Refresh Storage Size
                    </flux:button>

                    <flux:button
                        wire:click="$refresh"
                        variant="ghost"
                        class="justify-center py-3"
                    >
                        <flux:icon.arrow-path class="size-5 mr-2" />
                        Refresh Statistics
                    </flux:button>
                </div>

                @if($isRegenerating && $regenerationTotal > 0)
                    <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <div class="flex items-center justify-between mb-3">
                            <div class="flex items-center">
                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                                <span class="text-sm font-medium text-blue-800">
                                    @if($isProcessingQueue)
                                        Processing batch jobs...
                                    @else
                                        Regenerating conversions...
                                    @endif
                                </span>
                            </div>
                            <span class="text-sm text-blue-600 font-medium">{{ $regenerationCurrent }}/{{ $regenerationTotal }}</span>
                        </div>

                        <div class="w-full bg-blue-200 rounded-full h-3 mb-2">
                            <div class="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out"
                                 style="width: {{ $regenerationProgress }}%"></div>
                        </div>

                        <div class="flex justify-between text-xs text-blue-600">
                            <span>{{ $regenerationProgress }}% complete</span>
                            @if($regenerationEstimatedCompletion)
                                <span>Est. completion: {{ $regenerationEstimatedCompletion->format('H:i:s') }}</span>
                            @endif
                        </div>

                        @if($regenerationStartTime)
                            <div class="mt-2 flex justify-between items-center">
                                <div class="text-xs text-blue-500">
                                    Started: {{ $regenerationStartTime->format('H:i:s') }}
                                    ({{ $regenerationStartTime->diffForHumans() }})
                                </div>
                                @if($isProcessingQueue)
                                    <flux:button
                                        wire:click="stopBatchProcessing"
                                        variant="outline"
                                        size="xs"
                                        class="text-red-600 border-red-300 hover:bg-red-50"
                                    >
                                        <flux:icon.stop class="size-3 mr-1" />
                                        Stop Processing
                                    </flux:button>
                                @endif
                            </div>
                        @endif
                    </div>
                @endif
            </div>

            <!-- Conversion Settings -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="mb-6">
                    <flux:heading size="lg">Conversion Settings</flux:heading>
                    <flux:subheading>Current image conversion configuration</flux:subheading>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="border rounded-lg p-4">
                        <flux:heading size="md" class="mb-2">Thumbnail Conversion</flux:heading>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div>Dimensions: 100x100 pixels</div>
                            <div>Quality: High (with sharpening)</div>
                            <div>Format: JPEG</div>
                            <div>Usage: Property listings, navigation</div>
                        </div>
                    </div>

                    <div class="border rounded-lg p-4">
                        <flux:heading size="md" class="mb-2">Preview Conversion</flux:heading>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div>Dimensions: 400x300 pixels</div>
                            <div>Quality: High (with sharpening)</div>
                            <div>Format: JPEG</div>
                            <div>Usage: Property details, search results</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Queue Management Tab -->
    @if($activeTab === 'queue')
        <div class="space-y-6">
            <!-- Queue Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="xl" class="text-blue-600">{{ $queueStats['pending_jobs'] }}</flux:heading>
                            <flux:subheading>Pending Jobs</flux:subheading>
                            <div class="text-xs text-gray-500 mt-1">
                                {{ $queueStats['media_jobs'] }} media jobs
                            </div>
                        </div>
                        <flux:icon.clock class="size-8 text-blue-500" />
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="xl" class="text-red-600">{{ $queueStats['failed_jobs'] }}</flux:heading>
                            <flux:subheading>Failed Jobs</flux:subheading>
                            <div class="text-xs text-gray-500 mt-1">
                                Need attention
                            </div>
                        </div>
                        <flux:icon.x-circle class="size-8 text-red-500" />
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="xl" class="{{ $queueStats['queue_status'] === 'running' ? 'text-green-600' : 'text-red-600' }}">
                                {{ ucfirst($queueStats['queue_status']) }}
                            </flux:heading>
                            <flux:subheading>Queue Worker</flux:subheading>
                            <div class="text-xs text-gray-500 mt-1">
                                Worker status
                            </div>
                        </div>
                        <flux:icon.cog-6-tooth class="size-8 {{ $queueStats['queue_status'] === 'running' ? 'text-green-500' : 'text-red-500' }}" />
                    </div>
                </div>

                <div class="bg-white p-6 rounded-lg shadow border">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="xl" class="text-purple-600">Database</flux:heading>
                            <flux:subheading>Queue Driver</flux:subheading>
                            <div class="text-xs text-gray-500 mt-1">
                                Current configuration
                            </div>
                        </div>
                        <flux:icon.circle-stack class="size-8 text-purple-500" />
                    </div>
                </div>
            </div>

            <!-- Queue Management Actions -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="mb-6">
                    <flux:heading size="lg">Queue Operations</flux:heading>
                    <flux:subheading>Process jobs and manage queue workers</flux:subheading>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <flux:button 
                        wire:click="processQueueJobs" 
                        variant="primary" 
                        class="justify-center py-3"
                    >
                        <flux:icon.play class="size-5 mr-2" />
                        Process Jobs
                    </flux:button>

                    <flux:button 
                        wire:click="retryFailedJobs" 
                        variant="outline" 
                        class="justify-center py-3"
                        :disabled="$queueStats['failed_jobs'] === 0"
                    >
                        <flux:icon.arrow-path class="size-5 mr-2" />
                        Retry Failed Jobs
                    </flux:button>

                    <flux:button 
                        wire:click="clearFailedJobs" 
                        variant="danger" 
                        class="justify-center py-3"
                        :disabled="$queueStats['failed_jobs'] === 0"
                    >
                        <flux:icon.trash class="size-5 mr-2" />
                        Clear Failed Jobs
                    </flux:button>

                    <flux:button 
                        wire:click="$refresh" 
                        variant="ghost" 
                        class="justify-center py-3"
                    >
                        <flux:icon.arrow-path class="size-5 mr-2" />
                        Refresh Status
                    </flux:button>
                </div>
            </div>

            <!-- Queue Worker Information -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <div class="mb-6">
                    <flux:heading size="lg">Queue Worker Setup</flux:heading>
                    <flux:subheading>Information about setting up persistent queue workers</flux:subheading>
                </div>

                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <flux:heading size="md" class="mb-2">For Development</flux:heading>
                        <code class="text-sm bg-gray-100 p-2 rounded block">php artisan queue:work --daemon</code>
                        <flux:text size="sm" class="mt-2">Run this command to start a queue worker that will process jobs continuously.</flux:text>
                    </div>

                    <div class="bg-gray-50 p-4 rounded-lg">
                        <flux:heading size="md" class="mb-2">For Production</flux:heading>
                        <flux:text size="sm">Consider using Supervisor or Laravel Horizon for production queue management.</flux:text>
                        <div class="mt-2">
                            <flux:button href="#" variant="outline" size="sm">
                                Install Laravel Horizon
                            </flux:button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Bulk Operations Tab -->
    @if($activeTab === 'bulk')
        <div class="space-y-6">
            <!-- Bulk Actions Header -->
            @if($showBulkActions)
                <div class="bg-blue-50 border border-blue-200 p-4 rounded-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="md" class="text-blue-800">{{ count($selectedProperties) }} properties selected</flux:heading>
                            <flux:text size="sm" class="text-blue-600">Choose an action to perform on selected properties</flux:text>
                        </div>
                        <div class="flex space-x-2">
                            <flux:button
                                wire:click="regenerateSelectedConversions"
                                variant="primary"
                                size="sm"
                            >
                                <flux:icon.arrow-path class="size-4 mr-1" />
                                Regenerate Conversions
                            </flux:button>
                            <flux:button
                                wire:click="$set('selectedProperties', []); $set('showBulkActions', false)"
                                variant="ghost"
                                size="sm"
                            >
                                Cancel
                            </flux:button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Properties List -->
            <div class="bg-white rounded-lg shadow border">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div>
                            <flux:heading size="lg">Properties with Media</flux:heading>
                            <flux:subheading>Select properties for bulk operations</flux:subheading>
                        </div>
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center">
                                <flux:checkbox
                                    wire:model.live="selectAll"
                                    wire:click="toggleSelectAll"
                                />
                                <span class="ml-2 text-sm text-gray-600">Select All</span>
                            </label>
                        </div>
                    </div>
                </div>

                <div class="divide-y divide-gray-200">
                    @forelse($properties as $property)
                        <div class="p-6 hover:bg-gray-50">
                            <div class="flex items-center space-x-4">
                                <flux:checkbox
                                    wire:model.live="selectedProperties"
                                    value="{{ $property->id }}"
                                />

                                <div class="flex-shrink-0">
                                    @if($property->getFirstMedia('gallery'))
                                        <img
                                            src="{{ $property->getFirstMediaUrl('gallery', 'thumb') }}"
                                            alt="{{ $property->title }}"
                                            class="w-16 h-16 object-cover rounded-lg"
                                        >
                                    @else
                                        <div class="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                                            <flux:icon.photo class="size-6 text-gray-400" />
                                        </div>
                                    @endif
                                </div>

                                <div class="flex-1 min-w-0">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <flux:heading size="md" class="truncate">{{ $property->title }}</flux:heading>
                                            <flux:text size="sm" class="text-gray-500">
                                                By {{ $property->user->name }} • {{ $property->media->count() }} media files
                                            </flux:text>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            @php
                                                $mediaWithConversions = $property->media->filter(function($media) {
                                                    return $media->hasGeneratedConversion('thumb') && $media->hasGeneratedConversion('preview');
                                                })->count();
                                                $totalMedia = $property->media->count();
                                                $conversionRate = $totalMedia > 0 ? round(($mediaWithConversions / $totalMedia) * 100) : 0;
                                            @endphp

                                            <div class="text-right">
                                                <div class="text-sm font-medium {{ $conversionRate === 100 ? 'text-green-600' : ($conversionRate > 0 ? 'text-orange-600' : 'text-red-600') }}">
                                                    {{ $conversionRate }}%
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    {{ $mediaWithConversions }}/{{ $totalMedia }} converted
                                                </div>
                                            </div>

                                            @if($conversionRate === 100)
                                                <flux:icon.check-circle class="size-5 text-green-500" />
                                            @elseif($conversionRate > 0)
                                                <flux:icon.exclamation-triangle class="size-5 text-orange-500" />
                                            @else
                                                <flux:icon.x-circle class="size-5 text-red-500" />
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="p-12 text-center">
                            <flux:icon.photo class="size-12 text-gray-400 mx-auto mb-4" />
                            <flux:heading size="lg" class="text-gray-500">No properties with media found</flux:heading>
                            <flux:text class="text-gray-400">Properties with uploaded images will appear here</flux:text>
                        </div>
                    @endforelse
                </div>

                @if($properties->hasPages())
                    <div class="p-6 border-t border-gray-200">
                        {{ $properties->links() }}
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Statistics Tab -->
    @if($activeTab === 'statistics')
        <div class="space-y-6">
            <!-- Detailed Statistics -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Media Overview -->
                <div class="bg-white p-6 rounded-lg shadow border">
                    <flux:heading size="lg" class="mb-4">Media Overview</flux:heading>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Total Media Files</span>
                            <span class="font-semibold">{{ $mediaStats['total_media'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">With Any Conversions</span>
                            <span class="font-semibold text-blue-600">{{ $mediaStats['with_conversions'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Complete Conversions</span>
                            <span class="font-semibold text-green-600">{{ $mediaStats['complete_conversions'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Missing Conversions</span>
                            <span class="font-semibold text-red-600">{{ $mediaStats['without_conversions'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Overall Success Rate</span>
                            <span class="font-semibold">{{ $mediaStats['conversion_success_rate'] }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Thumb Success Rate</span>
                            <span class="font-semibold text-purple-600">{{ $mediaStats['thumb_success_rate'] }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Preview Success Rate</span>
                            <span class="font-semibold text-indigo-600">{{ $mediaStats['preview_success_rate'] }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Storage Used</span>
                            <span class="font-semibold">{{ $mediaStats['storage_size'] }}</span>
                        </div>
                    </div>
                </div>

                <!-- Queue Statistics -->
                <div class="bg-white p-6 rounded-lg shadow border">
                    <flux:heading size="lg" class="mb-4">Queue Statistics</flux:heading>
                    <div class="space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Pending Jobs</span>
                            <span class="font-semibold">{{ $queueStats['pending_jobs'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Media Jobs</span>
                            <span class="font-semibold text-blue-600">{{ $queueStats['media_jobs'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Failed Jobs (Total)</span>
                            <span class="font-semibold text-red-600">{{ $queueStats['failed_jobs'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Failed Media Jobs</span>
                            <span class="font-semibold text-red-600">{{ $queueStats['failed_media_jobs'] }}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Media Job Success Rate</span>
                            <span class="font-semibold text-green-600">{{ $queueStats['media_job_success_rate'] }}%</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600">Worker Status</span>
                            <span class="font-semibold {{ $queueStats['queue_status'] === 'running' ? 'text-green-600' : 'text-red-600' }}">
                                {{ ucfirst($queueStats['queue_status']) }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Conversion Breakdown -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <flux:heading size="lg" class="mb-4">Conversion Breakdown</flux:heading>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <flux:subheading class="mb-3">Thumbnail Conversions</flux:subheading>
                        <div class="flex items-center space-x-4">
                            <div class="flex-1 bg-gray-200 rounded-full h-3">
                                @php
                                    $thumbPercentage = $mediaStats['total_media'] > 0 ? ($mediaStats['thumb_conversions'] / $mediaStats['total_media']) * 100 : 0;
                                @endphp
                                <div class="bg-blue-600 h-3 rounded-full" style="width: {{ $thumbPercentage }}%"></div>
                            </div>
                            <span class="text-sm font-medium">{{ round($thumbPercentage, 1) }}%</span>
                        </div>
                        <flux:text size="sm" class="text-gray-500 mt-1">
                            {{ $mediaStats['thumb_conversions'] }} of {{ $mediaStats['total_media'] }} files
                        </flux:text>
                    </div>

                    <div>
                        <flux:subheading class="mb-3">Preview Conversions</flux:subheading>
                        <div class="flex items-center space-x-4">
                            <div class="flex-1 bg-gray-200 rounded-full h-3">
                                @php
                                    $previewPercentage = $mediaStats['total_media'] > 0 ? ($mediaStats['preview_conversions'] / $mediaStats['total_media']) * 100 : 0;
                                @endphp
                                <div class="bg-green-600 h-3 rounded-full" style="width: {{ $previewPercentage }}%"></div>
                            </div>
                            <span class="text-sm font-medium">{{ round($previewPercentage, 1) }}%</span>
                        </div>
                        <flux:text size="sm" class="text-gray-500 mt-1">
                            {{ $mediaStats['preview_conversions'] }} of {{ $mediaStats['total_media'] }} files
                        </flux:text>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="bg-white p-6 rounded-lg shadow border">
                <flux:heading size="lg" class="mb-4">System Information</flux:heading>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Queue Driver</span>
                            <span class="font-medium">Database</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Image Driver</span>
                            <span class="font-medium">GD/Imagick</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Storage Disk</span>
                            <span class="font-medium">Public</span>
                        </div>
                    </div>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Media Library</span>
                            <span class="font-medium">Spatie</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Conversion Types</span>
                            <span class="font-medium">Thumb, Preview</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Collection</span>
                            <span class="font-medium">Gallery</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
